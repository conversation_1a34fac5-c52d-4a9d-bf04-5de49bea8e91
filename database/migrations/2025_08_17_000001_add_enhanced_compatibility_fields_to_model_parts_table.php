<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            // Add new compatibility fields for enhanced CSV format
            $table->string('front_camera_type')->nullable()->after('location');
            $table->string('camera_position')->nullable()->after('front_camera_type');
            $table->string('battery_mah')->nullable()->after('camera_position');
            $table->string('pin_model')->nullable()->after('battery_mah');
            $table->string('connector_types')->nullable()->after('pin_model');
            $table->string('types')->nullable()->after('connector_types');
            $table->text('additional_info')->nullable()->after('types');

            // Add indexes for better query performance on searchable fields
            $table->index('front_camera_type');
            $table->index('camera_position');
            $table->index('battery_mah');
            $table->index('connector_types');
            $table->index('types');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['front_camera_type']);
            $table->dropIndex(['camera_position']);
            $table->dropIndex(['battery_mah']);
            $table->dropIndex(['connector_types']);
            $table->dropIndex(['types']);

            // Drop columns
            $table->dropColumn([
                'front_camera_type',
                'camera_position',
                'battery_mah',
                'pin_model',
                'connector_types',
                'types',
                'additional_info'
            ]);
        });
    }
};
