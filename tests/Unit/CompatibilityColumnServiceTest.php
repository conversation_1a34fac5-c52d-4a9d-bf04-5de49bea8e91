<?php

namespace Tests\Unit;

use App\Models\SiteSetting;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CompatibilityColumnServiceTest extends TestCase
{
    use RefreshDatabase;

    private CompatibilityColumnService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CompatibilityColumnService();
        Cache::flush();

        // Clear any existing settings from migration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
    }

    public function test_get_column_configuration_returns_default_when_no_setting_exists()
    {
        $config = $this->service->getColumnConfiguration();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('brand', $config);
        $this->assertArrayHasKey('model', $config);
        $this->assertTrue($config['brand']['enabled']);
        $this->assertTrue($config['model']['enabled']);
    }

    public function test_get_column_configuration_filters_disabled_columns_for_public()
    {
        // Create a setting with some disabled columns
        $testConfig = [
            'brand' => ['enabled' => true, 'order' => 1, 'label' => 'Brand'],
            'model' => ['enabled' => true, 'order' => 2, 'label' => 'Model'],
            'notes' => ['enabled' => false, 'order' => 3, 'label' => 'Notes'],
        ];
        
        SiteSetting::create([
            'key' => 'parts_compatibility_columns',
            'value' => $testConfig,
            'type' => 'json',
            'category' => 'parts_management',
            'is_active' => true,
        ]);

        $publicConfig = $this->service->getColumnConfiguration(false);
        $adminConfig = $this->service->getColumnConfiguration(true);

        // Public should only have enabled columns
        $this->assertCount(2, $publicConfig);
        $this->assertArrayHasKey('brand', $publicConfig);
        $this->assertArrayHasKey('model', $publicConfig);
        $this->assertArrayNotHasKey('notes', $publicConfig);

        // Admin should have all columns
        $this->assertCount(3, $adminConfig);
        $this->assertArrayHasKey('notes', $adminConfig);
    }

    public function test_get_visible_columns_sorts_by_order()
    {
        $testConfig = [
            'model' => ['enabled' => true, 'order' => 2, 'label' => 'Model'],
            'brand' => ['enabled' => true, 'order' => 1, 'label' => 'Brand'],
            'notes' => ['enabled' => true, 'order' => 3, 'label' => 'Notes'],
        ];
        
        SiteSetting::create([
            'key' => 'parts_compatibility_columns',
            'value' => $testConfig,
            'type' => 'json',
            'category' => 'parts_management',
            'is_active' => true,
        ]);

        $columns = $this->service->getVisibleColumns();
        $keys = array_keys($columns);

        $this->assertEquals(['brand', 'model', 'notes'], $keys);
    }

    public function test_get_column_value_returns_correct_values()
    {
        $model = [
            'brand' => ['name' => 'Apple'],
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'pivot' => [
                'compatibility_notes' => 'Compatible with all variants',
                'is_verified' => true,
                'display_type' => 'OLED',
                'display_size' => '6.1 inches',
                'location' => 'Front',
                'front_camera_type' => '12MP',
                'camera_position' => 'Front',
                'battery_mah' => '3095',
                'pin_model' => 'Lightning',
                'connector_types' => 'USB-C',
                'types' => 'Premium',
                'additional_info' => 'Additional info'
            ]
        ];

        $part = [
            'name' => 'Screen Assembly',
            'part_number' => 'SCR-001',
            'manufacturer' => 'OEM',
            'category' => ['name' => 'Display']
        ];

        // Test brand name
        $config = ['source' => 'model.brand.name'];
        $value = $this->service->getColumnValue('brand', $config, $model, $part);
        $this->assertEquals('Apple', $value);

        // Test model name
        $config = ['source' => 'model.name'];
        $value = $this->service->getColumnValue('model', $config, $model, $part);
        $this->assertEquals('iPhone 13', $value);

        // Test part name
        $config = ['source' => 'part.name'];
        $value = $this->service->getColumnValue('part_name', $config, $model, $part);
        $this->assertEquals('Screen Assembly', $value);

        // Test pivot fields
        $config = ['source' => 'model.pivot.display_type'];
        $value = $this->service->getColumnValue('display_type', $config, $model, $part);
        $this->assertEquals('OLED', $value);

        // Test verified status
        $config = ['source' => 'model.pivot.is_verified'];
        $value = $this->service->getColumnValue('verified', $config, $model, $part);
        $this->assertTrue($value === true || $value === 1 || $value === '1');

        // Test new pivot fields
        $config = ['source' => 'model.pivot.front_camera_type'];
        $value = $this->service->getColumnValue('front_camera_type', $config, $model, $part);
        $this->assertEquals('12MP', $value);

        $config = ['source' => 'model.pivot.camera_position'];
        $value = $this->service->getColumnValue('camera_position', $config, $model, $part);
        $this->assertEquals('Front', $value);

        $config = ['source' => 'model.pivot.battery_mah'];
        $value = $this->service->getColumnValue('battery_mah', $config, $model, $part);
        $this->assertEquals('3095', $value);

        $config = ['source' => 'model.pivot.pin_model'];
        $value = $this->service->getColumnValue('pin_model', $config, $model, $part);
        $this->assertEquals('Lightning', $value);

        $config = ['source' => 'model.pivot.connector_types'];
        $value = $this->service->getColumnValue('connector_types', $config, $model, $part);
        $this->assertEquals('USB-C', $value);

        $config = ['source' => 'model.pivot.types'];
        $value = $this->service->getColumnValue('types', $config, $model, $part);
        $this->assertEquals('Premium', $value);

        $config = ['source' => 'model.pivot.additional_info'];
        $value = $this->service->getColumnValue('additional_info', $config, $model, $part);
        $this->assertEquals('Additional info', $value);
    }

    public function test_get_responsive_class_returns_correct_classes()
    {
        $testCases = [
            ['minBreakpoint' => 'xs', 'expected' => ''],
            ['minBreakpoint' => 'sm', 'expected' => 'hidden sm:table-cell'],
            ['minBreakpoint' => 'md', 'expected' => 'hidden md:table-cell'],
            ['minBreakpoint' => 'lg', 'expected' => 'hidden lg:table-cell'],
            ['minBreakpoint' => 'xl', 'expected' => 'hidden xl:table-cell'],
        ];

        foreach ($testCases as $testCase) {
            $config = ['minBreakpoint' => $testCase['minBreakpoint']];
            $class = $this->service->getResponsiveClass($config);
            $this->assertEquals($testCase['expected'], $class);
        }
    }

    public function test_update_column_configuration_updates_setting_and_clears_cache()
    {
        $newConfig = [
            'brand' => ['enabled' => true, 'order' => 1],
            'model' => ['enabled' => false, 'order' => 2],
        ];

        $result = $this->service->updateColumnConfiguration($newConfig);
        $this->assertTrue($result);

        // Verify setting was updated
        $setting = SiteSetting::where('key', 'parts_compatibility_columns')->first();
        $this->assertNotNull($setting);
        $this->assertEquals($newConfig, $setting->value);

        // Verify cache was cleared by checking if new config is returned
        $config = $this->service->getColumnConfiguration(true);
        $this->assertEquals($newConfig, $config);
    }

    public function test_validate_configuration_returns_errors_for_disabled_required_columns()
    {
        $invalidConfig = [
            'brand' => ['enabled' => false, 'required' => true],
            'model' => ['enabled' => true, 'required' => true],
        ];

        $errors = $this->service->validateConfiguration($invalidConfig);
        
        $this->assertCount(1, $errors);
        $this->assertStringContainsString('Required column \'brand\' must be enabled', $errors[0]);
    }

    public function test_validate_configuration_returns_no_errors_for_valid_config()
    {
        $validConfig = [
            'brand' => ['enabled' => true, 'required' => true],
            'model' => ['enabled' => true, 'required' => true],
            'notes' => ['enabled' => false, 'required' => false],
        ];

        $errors = $this->service->validateConfiguration($validConfig);
        
        $this->assertEmpty($errors);
    }
}
