<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CompatibilityColumnIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private Part $part;
    private MobileModel $model;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->user = User::factory()->create(['role' => 'user']);
        $this->columnService = app(CompatibilityColumnService::class);

        // Create test data
        $brand = Brand::factory()->create(['name' => 'Test Brand']);
        $category = Category::factory()->create(['name' => 'Test Category']);
        
        $this->model = MobileModel::factory()->create([
            'name' => 'Test Model',
            'brand_id' => $brand->id
        ]);
        
        $this->part = Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $category->id
        ]);

        // Attach part to model with compatibility data
        $this->part->models()->attach($this->model->id, [
            'is_verified' => true,
            'front_camera_type' => 'Standard',
            'camera_position' => 'Center',
            'battery_mah' => 4000,
            'pin_model' => 'Type-A',
            'connector_types' => json_encode(['USB-C', 'Lightning']),
            'types' => json_encode(['Replacement', 'OEM']),
            'additional_info' => 'Test compatibility info'
        ]);
    }

    /** @test */
    public function admin_part_view_includes_column_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}");

        $response->assertOk();
        
        // Check that the page includes compatibility columns configuration
        $response->assertInertia(fn ($page) => 
            $page->has('compatibilityColumns')
                ->where('isAdminView', true)
        );
    }

    /** @test */
    public function public_part_view_includes_filtered_column_configuration()
    {
        $response = $this->actingAs($this->user)
            ->get("/parts/{$this->part->id}");

        $response->assertOk();
        
        // Check that the page includes only enabled columns
        $response->assertInertia(fn ($page) => 
            $page->has('compatibilityColumns')
                ->where('isAdminView', false)
        );
    }

    /** @test */
    public function admin_compatibility_page_includes_column_management()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/parts/compatibility');

        $response->assertOk();
        
        // Should include column configuration for admin
        $response->assertInertia(fn ($page) => 
            $page->has('compatibilityColumns')
                ->where('isAdminView', true)
        );
    }

    /** @test */
    public function site_settings_page_includes_parts_management_tab()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/site-settings');

        $response->assertOk();
        
        // Should include parts_compatibility_columns setting
        $response->assertInertia(fn ($page) => 
            $page->has('settings.parts_management')
        );
    }

    /** @test */
    public function column_configuration_affects_table_rendering()
    {
        // Disable brand_name column
        $config = $this->columnService->getColumnConfiguration(true);
        $config['brand_name']['enabled'] = false;
        $this->columnService->updateColumnConfiguration($config);

        // Public view should not include brand_name
        $publicConfig = $this->columnService->getColumnConfiguration(false);
        $this->assertArrayNotHasKey('brand_name', $publicConfig);

        // Admin view should still include brand_name
        $adminConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertArrayHasKey('brand_name', $adminConfig);
        $this->assertFalse($adminConfig['brand_name']['enabled']);
    }

    /** @test */
    public function column_toggle_api_works_with_frontend()
    {
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        $initialState = $initialConfig['brand_name']['enabled'];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/toggle', [
                'column' => 'brand_name'
            ]);

        $response->assertOk();

        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertNotEquals($initialState, $updatedConfig['brand_name']['enabled']);
    }

    /** @test */
    public function column_configuration_persists_across_requests()
    {
        $newConfig = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Custom Model Label',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand_name' => [
                'enabled' => false,
                'required' => false,
                'order' => 2,
                'label' => 'Custom Brand Label',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'sm'
            ]
        ];

        // Update configuration
        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $newConfig
            ]);

        // Verify it persists in subsequent requests
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/parts/compatibility-columns/config');

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'data' => $newConfig
            ]);
    }

    /** @test */
    public function column_order_affects_display_order()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Change order of columns
        $config['model_name']['order'] = 10;
        $config['brand_name']['order'] = 1;
        
        $this->columnService->updateColumnConfiguration($config);

        $sortedColumns = $this->columnService->getVisibleColumns(true);
        $columnKeys = array_keys($sortedColumns);
        
        // brand_name should come before model_name now
        $brandIndex = array_search('brand_name', $columnKeys);
        $modelIndex = array_search('model_name', $columnKeys);
        
        $this->assertLessThan($modelIndex, $brandIndex);
    }

    /** @test */
    public function responsive_classes_are_applied_correctly()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Test different breakpoints
        $testCases = [
            'xs' => '',
            'sm' => 'hidden sm:table-cell',
            'md' => 'hidden md:table-cell',
            'lg' => 'hidden lg:table-cell',
            'xl' => 'hidden xl:table-cell'
        ];

        foreach ($testCases as $breakpoint => $expectedClass) {
            $columnConfig = ['minBreakpoint' => $breakpoint];
            $actualClass = $this->columnService->getResponsiveClass($columnConfig);
            $this->assertEquals($expectedClass, $actualClass);
        }
    }

    /** @test */
    public function column_values_are_extracted_correctly()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Test model name extraction
        $modelValue = $this->columnService->getColumnValue(
            'model_name',
            $config['model_name'],
            $this->model,
            $this->part
        );
        $this->assertEquals('Test Model', $modelValue);

        // Test brand name extraction
        $brandValue = $this->columnService->getColumnValue(
            'brand_name',
            $config['brand_name'],
            $this->model,
            $this->part
        );
        $this->assertEquals('Test Brand', $brandValue);
    }

    /** @test */
    public function pivot_data_is_extracted_correctly()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Test pivot data extraction
        $verifiedValue = $this->columnService->getColumnValue(
            'verified',
            $config['verified'],
            $this->model,
            $this->part
        );
        $this->assertEquals('Verified', $verifiedValue);

        $batteryValue = $this->columnService->getColumnValue(
            'battery_mah',
            $config['battery_mah'],
            $this->model,
            $this->part
        );
        $this->assertEquals('4000 mAh', $batteryValue);
    }

    /** @test */
    public function cache_invalidation_works_correctly()
    {
        // Prime the cache
        $this->columnService->getColumnConfiguration();
        $this->assertTrue(Cache::has('compatibility_columns_config'));

        // Update configuration via API
        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $this->columnService->getColumnConfiguration(true)
            ]);

        // Cache should be cleared
        $this->assertFalse(Cache::has('compatibility_columns_config'));
    }

    /** @test */
    public function fallback_to_hardcoded_table_when_no_config()
    {
        // Remove configuration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
        Cache::forget('compatibility_columns_config');

        $response = $this->actingAs($this->user)
            ->get("/parts/{$this->part->id}");

        $response->assertOk();
        
        // Should still render the page with default configuration
        $response->assertInertia(fn ($page) => 
            $page->has('compatibilityColumns')
        );
    }
}
