<?php

namespace Tests\Feature\Admin;

use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CompatibilityColumnManagementTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->user = User::factory()->create(['role' => 'user']);
        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function admin_can_access_column_configuration_endpoint()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/admin/parts/compatibility-columns/config');

        $response->assertOk()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'model' => [
                        'enabled',
                        'required',
                        'order',
                        'label',
                        'source',
                        'priority',
                        'minBreakpoint'
                    ]
                ]
            ]);
    }

    /** @test */
    public function non_admin_cannot_access_column_configuration_endpoint()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/admin/parts/compatibility-columns/config');

        $response->assertForbidden();
    }

    /** @test */
    public function guest_cannot_access_column_configuration_endpoint()
    {
        $response = $this->getJson('/admin/parts/compatibility-columns/config');

        $response->assertUnauthorized();
    }

    /** @test */
    public function admin_can_update_column_configuration()
    {
        $newConfig = [
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Device Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand' => [
                'enabled' => false,
                'required' => false,
                'order' => 2,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'sm'
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $newConfig
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'message' => 'Column configuration updated successfully'
            ]);

        // Verify the configuration was saved
        $savedConfig = SiteSetting::get('parts_compatibility_columns');
        $this->assertEquals($newConfig, $savedConfig);
    }

    /** @test */
    public function admin_can_toggle_individual_column()
    {
        // Set initial configuration
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        
        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/toggle', [
                'column' => 'brand'
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => true
            ]);

        // Verify the column was toggled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertNotEquals(
            $initialConfig['brand']['enabled'],
            $updatedConfig['brand']['enabled']
        );
    }

    /** @test */
    public function cannot_disable_required_columns()
    {
        // Ensure model is required and enabled
        $config = $this->columnService->getColumnConfiguration(true);
        $config['model']['required'] = true;
        $config['model']['enabled'] = true;
        $this->columnService->updateColumnConfiguration($config);

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/toggle', [
                'column' => 'model'
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Required columns cannot be disabled'
            ]);
    }

    /** @test */
    public function column_configuration_validation_works()
    {
        $invalidConfig = [
            'model' => [
                'enabled' => 'not_boolean',
                'required' => 'not_boolean',
                'order' => 'not_integer',
                'label' => '',
                'source' => '',
                'priority' => 'not_integer',
                'minBreakpoint' => 'invalid_breakpoint'
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $invalidConfig
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'columns.model.enabled',
                'columns.model.required',
                'columns.model.order',
                'columns.model.label',
                'columns.model.source',
                'columns.model.priority',
                'columns.model.minBreakpoint'
            ]);
    }

    /** @test */
    public function cache_is_cleared_when_configuration_is_updated()
    {
        // Prime the cache
        $this->columnService->getColumnConfiguration();
        $this->assertTrue(Cache::has('compatibility_columns_config'));

        $newConfig = [
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Updated Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ]
        ];

        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $newConfig
            ]);

        // Cache should be cleared
        $this->assertFalse(Cache::has('compatibility_columns_config'));
    }

    /** @test */
    public function column_configuration_returns_validation_errors()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Disable all required columns to trigger validation error
        foreach ($config as $key => $column) {
            if ($column['required']) {
                $config[$key]['enabled'] = false;
            }
        }

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/validate', [
                'columns' => $config
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => false,
                'errors' => [
                    'At least one required column must be enabled'
                ]
            ]);
    }

    /** @test */
    public function column_configuration_passes_validation_with_valid_config()
    {
        $config = $this->columnService->getColumnConfiguration(true);

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/validate', [
                'columns' => $config
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'errors' => []
            ]);
    }

    /** @test */
    public function column_service_returns_correct_configuration_for_admin_mode()
    {
        $config = $this->columnService->getColumnConfiguration(true);
        
        // Admin mode should return all columns regardless of enabled status
        $this->assertArrayHasKey('model', $config);
        $this->assertArrayHasKey('brand', $config);
        
        // Should include disabled columns
        $disabledColumns = array_filter($config, fn($col) => !$col['enabled']);
        $this->assertNotEmpty($disabledColumns);
    }

    /** @test */
    public function column_service_returns_correct_configuration_for_public_mode()
    {
        $config = $this->columnService->getColumnConfiguration(false);
        
        // Public mode should only return enabled columns
        foreach ($config as $column) {
            $this->assertTrue($column['enabled']);
        }
    }

    /** @test */
    public function column_service_sorts_columns_by_order()
    {
        $config = $this->columnService->getVisibleColumns(true);
        $orders = array_column($config, 'order');
        
        // Should be sorted by order
        $sortedOrders = $orders;
        sort($sortedOrders);
        $this->assertEquals($sortedOrders, array_values($orders));
    }

    /** @test */
    public function column_service_handles_missing_configuration_gracefully()
    {
        // Remove the configuration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
        Cache::forget('compatibility_columns_config');

        $config = $this->columnService->getColumnConfiguration();
        
        // Should return default configuration
        $this->assertNotEmpty($config);
        $this->assertArrayHasKey('model', $config);
    }
}
