<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class CsvColumnSelectionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable export/import functionality for tests
        \App\Models\SiteSetting::updateOrCreate(
            ['key' => 'admin_parts_export_import_enabled'],
            ['value' => '1', 'type' => 'boolean', 'is_active' => true]
        );

        // Clear all related caches
        \Illuminate\Support\Facades\Cache::flush();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model'
        ]);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Part'
        ]);
    }

    public function test_preview_endpoint_returns_column_information()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with various column types
        $csvContent = "Brand,Model,Model Number,Compatible,Verified,Display Type,Display Size,Location,Notes\n";
        $csvContent .= "Apple,iPhone 13,A2482,true,true,OLED,6.1 inches,Front,Test notes\n";
        $csvContent .= "Samsung,Galaxy S21,SM-G991B,true,false,AMOLED,6.2 inches,Front,Another note\n";
        
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file
        ]);
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'columns' => [
                '*' => [
                    'name',
                    'sampleData',
                    'isRequired',
                    'mappedTo',
                    'purpose',
                    'isSelected'
                ]
            ],
            'totalRows',
            'previewRows'
        ]);
        
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertCount(9, $data['columns']);
        $this->assertEquals(2, $data['totalRows']);
        
        // Check that required columns are detected
        $requiredColumns = collect($data['columns'])->where('isRequired', true);
        $this->assertCount(3, $requiredColumns); // Brand, Model, Compatible
        
        // Check column mapping
        $brandColumn = collect($data['columns'])->firstWhere('name', 'Brand');
        $this->assertEquals('Brand', $brandColumn['mappedTo']);
        $this->assertTrue($brandColumn['isRequired']);
        $this->assertTrue($brandColumn['isSelected']);
    }

    public function test_preview_endpoint_handles_invalid_files()
    {
        $this->actingAs($this->admin);
        
        // Test empty file
        $file = UploadedFile::fake()->createWithContent('empty.csv', '');
        
        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file
        ]);
        
        $response->assertStatus(400);
        $response->assertJson([
            'success' => false,
            'message' => 'The CSV file appears to be empty.'
        ]);
    }

    public function test_preview_endpoint_handles_header_only_files()
    {
        $this->actingAs($this->admin);
        
        // Test file with only headers
        $csvContent = "Brand,Model,Compatible\n";
        $file = UploadedFile::fake()->createWithContent('headers_only.csv', $csvContent);
        
        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file
        ]);
        
        $response->assertStatus(200);
        $data = $response->json();
        $this->assertTrue($data['success']);
        $this->assertEquals(0, $data['totalRows']);
        $this->assertEmpty($data['previewRows']);
    }

    public function test_column_detection_maps_variations_correctly()
    {
        $this->actingAs($this->admin);
        
        // Test various column name variations
        $csvContent = "brand,model name,is compatible,is_verified,display_type,screen size,notes\n";
        $csvContent .= "Apple,iPhone 13,true,true,OLED,6.1 inches,Test\n";
        
        $file = UploadedFile::fake()->createWithContent('variations.csv', $csvContent);
        
        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file
        ]);
        
        $response->assertStatus(200);
        $data = $response->json();
        
        // Check that variations are mapped correctly
        $columns = collect($data['columns']);
        
        $brandCol = $columns->firstWhere('name', 'brand');
        $this->assertEquals('Brand', $brandCol['mappedTo']);
        
        $modelCol = $columns->firstWhere('name', 'model name');
        $this->assertEquals('Model', $modelCol['mappedTo']);
        
        $compatibleCol = $columns->firstWhere('name', 'is compatible');
        $this->assertEquals('Compatible', $compatibleCol['mappedTo']);
        
        $verifiedCol = $columns->firstWhere('name', 'is_verified');
        $this->assertEquals('Verified', $verifiedCol['mappedTo']);
        
        $displayTypeCol = $columns->firstWhere('name', 'display_type');
        $this->assertEquals('Display Type', $displayTypeCol['mappedTo']);
        
        $screenSizeCol = $columns->firstWhere('name', 'screen size');
        $this->assertEquals('Display Size', $screenSizeCol['mappedTo']);
    }

    public function test_import_with_selected_columns_works()
    {
        $this->actingAs($this->admin);
        
        // Create CSV with extra columns
        $csvContent = "Brand,Model,Model Number,Compatible,Verified,Display Type,Extra Column,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->model->model_number},true,true,OLED,ignore this,Test notes\n";
        
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        // Import with only selected columns
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
            'selected_columns' => ['Brand', 'Model', 'Compatible', 'Verified', 'Display Type', 'Notes']
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('Test notes', $compatibility->pivot->compatibility_notes);
        $this->assertEquals(1, $compatibility->pivot->is_verified);
    }

    public function test_import_fails_without_required_columns()
    {
        $this->actingAs($this->admin);
        
        $csvContent = "Brand,Model,Display Type,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},OLED,Test notes\n";
        
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        // Try to import without Compatible column
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
            'selected_columns' => ['Brand', 'Model', 'Display Type', 'Notes']
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHasErrors(['file']);
        
        $errors = session('errors')->get('file');
        $this->assertStringContainsString('Missing required columns: Compatible', $errors[0]);
    }

    public function test_import_maintains_backward_compatibility()
    {
        $this->actingAs($this->admin);
        
        // Test import without selected_columns parameter (old behavior)
        $csvContent = "Brand,Model,Model Number,Compatible,Verified,Display Type,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->model->model_number},true,true,OLED,Test notes\n";
        
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file
            // No selected_columns parameter
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('Test notes', $compatibility->pivot->compatibility_notes);
    }

    public function test_preview_handles_large_sample_data()
    {
        $this->actingAs($this->admin);
        
        // Create CSV with many rows to test sample data limiting
        $csvContent = "Brand,Model,Compatible\n";
        for ($i = 1; $i <= 10; $i++) {
            $csvContent .= "Brand{$i},Model{$i},true\n";
        }
        
        $file = UploadedFile::fake()->createWithContent('large.csv', $csvContent);
        
        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file
        ]);
        
        $response->assertStatus(200);
        $data = $response->json();
        
        // Should limit preview rows to 5
        $this->assertLessThanOrEqual(5, count($data['previewRows']));
        $this->assertEquals(10, $data['totalRows']);
        
        // Sample data should be limited to 3 unique values
        $brandColumn = collect($data['columns'])->firstWhere('name', 'Brand');
        $this->assertLessThanOrEqual(3, count($brandColumn['sampleData']));
    }
}
