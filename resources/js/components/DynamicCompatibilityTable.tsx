import { Badge } from '@/components/ui/badge';
import type { ColumnConfig, CompatibilityColumns, MobileModel, Part } from '@/types';
import { AlertCircle, Check, CheckCircle, Smartphone, X } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

interface DynamicCompatibilityTableProps {
    part: Part;
    compatibilityColumns: CompatibilityColumns;
    isAdminView?: boolean;
    className?: string;
    enableColumnResize?: boolean; // Enable column resizing functionality
}

const DynamicCompatibilityTable: React.FC<DynamicCompatibilityTableProps> = ({
    part,
    compatibilityColumns,
    isAdminView = false,
    className = '',
    enableColumnResize = false
}) => {
    // State for column resizing
    const [columnWidths, setColumnWidths] = useState<Record<string, string>>({});
    const [isResizing, setIsResizing] = useState<string | null>(null);
    const [startX, setStartX] = useState<number>(0);
    const [startWidth, setStartWidth] = useState<number>(0);
    const [forceUpdate, setForceUpdate] = useState<number>(0);
    const tableRef = useRef<HTMLTableElement>(null);

    // State for column configuration
    const [localCompatibilityColumns, setLocalCompatibilityColumns] = useState<CompatibilityColumns>(compatibilityColumns);
    const [isUpdatingColumn, setIsUpdatingColumn] = useState<string | null>(null);

    // Update local state when props change
    useEffect(() => {
        setLocalCompatibilityColumns(compatibilityColumns);
    }, [compatibilityColumns]);

    // Handle column toggle
    const handleColumnToggle = useCallback(async (columnKey: string) => {
        if (!isAdminView || isUpdatingColumn) return;

        const column = localCompatibilityColumns[columnKey];
        if (!column) return;

        // Don't allow disabling required columns
        if (column.required && column.enabled) {
            toast.error('Required columns cannot be disabled');
            return;
        }

        setIsUpdatingColumn(columnKey);

        try {
            const updatedColumns = {
                ...localCompatibilityColumns,
                [columnKey]: {
                    ...column,
                    enabled: !column.enabled
                }
            };

            // Optimistically update local state
            setLocalCompatibilityColumns(updatedColumns);

            // Make API call to update configuration
            const response = await fetch('/admin/parts/compatibility-columns/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    columns: updatedColumns
                })
            });

            const result = await response.json();

            if (!result.success) {
                // Revert optimistic update on error
                setLocalCompatibilityColumns(localCompatibilityColumns);
                toast.error(result.message || 'Failed to update column configuration');
                return;
            }

            // Update with server response
            setLocalCompatibilityColumns(result.data);
            toast.success(`Column "${column.label}" ${column.enabled ? 'disabled' : 'enabled'} successfully`);

        } catch (error) {
            // Revert optimistic update on error
            setLocalCompatibilityColumns(localCompatibilityColumns);
            toast.error('Failed to update column configuration');
            console.error('Column toggle error:', error);
        } finally {
            setIsUpdatingColumn(null);
        }
    }, [isAdminView, localCompatibilityColumns, isUpdatingColumn]);

    // ResizeObserver to detect column width changes and update column widths
    useEffect(() => {
        if (!tableRef.current) return;

        const updateColumnWidths = () => {
            if (!tableRef.current) return;

            const headers = tableRef.current.querySelectorAll('th[data-column-key]');
            const newWidths: Record<string, string> = {};

            headers.forEach((header) => {
                const columnKey = header.getAttribute('data-column-key');
                if (columnKey) {
                    const rect = header.getBoundingClientRect();
                    newWidths[columnKey] = `${rect.width}px`;
                }
            });

            setColumnWidths(prev => {
                // Only update if widths have actually changed
                const hasChanged = Object.keys(newWidths).some(key =>
                    prev[key] !== newWidths[key]
                );

                if (hasChanged) {
                    setForceUpdate(prev => prev + 1);
                    return newWidths;
                }
                return prev;
            });
        };

        const resizeObserver = new ResizeObserver(() => {
            // Update column widths when table size changes
            updateColumnWidths();
        });

        // Initial width calculation
        updateColumnWidths();

        resizeObserver.observe(tableRef.current);

        return () => {
            resizeObserver.disconnect();
        };
    }, [enableColumnResize]);

    // Update column widths when resizing ends
    useEffect(() => {
        if (!isResizing && tableRef.current) {
            // Update actual column widths after manual resizing
            const headers = tableRef.current.querySelectorAll('th[data-column-key]');
            const newWidths: Record<string, string> = {};

            headers.forEach((header) => {
                const columnKey = header.getAttribute('data-column-key');
                if (columnKey) {
                    const rect = header.getBoundingClientRect();
                    newWidths[columnKey] = `${rect.width}px`;
                }
            });

            setColumnWidths(newWidths);
            setForceUpdate(prev => prev + 1);
        }
    }, [isResizing]);
    // Column resizing handlers
    const handleMouseDown = useCallback((e: React.MouseEvent, columnKey: string) => {
        if (!enableColumnResize) return;

        e.preventDefault();
        e.stopPropagation();

        // Find the th element
        const th = e.currentTarget.closest('th');
        if (!th) return;

        // Store initial state
        setIsResizing(columnKey);
        setStartX(e.clientX);
        setStartWidth(th.offsetWidth);

        // Add visual feedback
        th.classList.add('resizing');

        // Prevent text selection during resize
        document.body.style.userSelect = 'none';
        document.body.style.cursor = 'col-resize';
    }, [enableColumnResize]);

    const handleMouseMove = useCallback((e: MouseEvent) => {
        if (!isResizing || !enableColumnResize) return;

        // Calculate new width with constraints
        const diff = e.clientX - startX;
        const minWidth = 80; // Minimum column width
        const newWidth = Math.max(minWidth, startWidth + diff);

        // Apply new width
        setColumnWidths(prev => ({
            ...prev,
            [isResizing]: `${newWidth}px`
        }));

        // Visual feedback during resize
        if (tableRef.current) {
            const th = tableRef.current.querySelector(`th[data-column-key="${isResizing}"]`) as HTMLElement;
            if (th) {
                th.style.width = `${newWidth}px`;
            }
        }
    }, [isResizing, startX, startWidth, enableColumnResize]);

    const handleMouseUp = useCallback(() => {
        // Remove visual feedback
        if (tableRef.current && isResizing) {
            const th = tableRef.current.querySelector(`th[data-column-key="${isResizing}"]`);
            if (th) {
                th.classList.remove('resizing');
            }
        }

        // Reset state
        setIsResizing(null);
        setStartX(0);
        setStartWidth(0);

        // Restore cursor and text selection
        document.body.style.userSelect = '';
        document.body.style.cursor = '';
    }, [isResizing]);

    // Add global mouse event listeners for resizing
    React.useEffect(() => {
        if (isResizing) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';

            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
            };
        }
    }, [isResizing, handleMouseMove, handleMouseUp]);

    // Get visible columns sorted by order
    const getVisibleColumns = () => {
        const columns = Object.entries(localCompatibilityColumns)
            .filter(([, config]) => isAdminView || config.enabled)
            .sort(([, a], [, b]) => (a.order || 999) - (b.order || 999));

        return columns;
    };

    // Calculate dynamic truncation based on column width
    const calculateTruncationLength = (columnKey: string, baseLength: number = 20): number => {
        const columnWidth = columnWidths[columnKey];
        if (!columnWidth) return baseLength;

        // Extract numeric value from width (e.g., "150px" -> 150)
        const widthValue = parseInt(columnWidth.replace(/[^\d]/g, ''));
        if (isNaN(widthValue)) return baseLength;

        // Calculate characters based on width (roughly 8px per character)
        const calculatedLength = Math.floor(widthValue / 8);
        return Math.max(calculatedLength, 10); // Minimum 10 characters
    };

    // Dynamic text truncation that responds to column width
    const truncateText = (text: string, columnKey: string, fallbackLength: number = 20): string => {
        if (!text) return text;

        const maxLength = calculateTruncationLength(columnKey, fallbackLength);
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    };

    // Get column width styles
    const getColumnWidthStyles = (columnKey: string, config: ColumnConfig, isHeader: boolean = false): React.CSSProperties => {
        const styles: React.CSSProperties = {};

        // Use resized width if available
        if (columnWidths[columnKey]) {
            styles.width = columnWidths[columnKey];
        } else if (config.width) {
            styles.width = config.width;
        }

        // For headers, ensure minimum width is sufficient for the label
        if (isHeader && config.label) {
            // Calculate minimum width based on label length
            const charWidth = 8; // Approximate width per character
            const padding = 40; // Extra padding for icons, etc.
            const labelWidth = Math.max(config.label.length * charWidth + padding, 100);

            // Use the larger of the calculated width or configured minWidth
            const configMinWidth = config.minWidth ? parseInt(config.minWidth) : 0;
            const effectiveMinWidth = Math.max(labelWidth, configMinWidth);

            styles.minWidth = `${effectiveMinWidth}px`;
        } else if (config.minWidth) {
            styles.minWidth = config.minWidth;
        }

        if (config.maxWidth && !columnWidths[columnKey]) {
            styles.maxWidth = config.maxWidth;
        }

        return styles;
    };

    // Get default column widths and responsive settings based on content type
    const getDefaultColumnConfig = (columnKey: string, config: ColumnConfig): Partial<ColumnConfig> => {
        // Calculate minimum width based on column label length to prevent truncation
        const getLabelBasedWidth = (label: string): string => {
            // Approximate width calculation: each character is roughly 8px + padding
            const charWidth = 8;
            const padding = 40; // Extra padding for icons, etc.
            const calculatedWidth = Math.max(label.length * charWidth + padding, 100);
            return `${calculatedWidth}px`;
        };

        // Get label from config or use columnKey as fallback
        const label = config.label || columnKey;
        const labelBasedWidth = getLabelBasedWidth(label);

        const defaults: Record<string, Partial<ColumnConfig>> = {
            // High priority columns - always visible
            'brand': {
                width: '120px', minWidth: '100px', maxWidth: '150px',
                truncate: true, priority: 1, minBreakpoint: 'xs'
            },
            'model': {
                width: '180px', minWidth: '150px', maxWidth: '250px',
                truncate: true, priority: 2, minBreakpoint: 'xs'
            },

            // Medium priority columns - hide on small screens
            'model_number': {
                width: '150px', minWidth: '130px', maxWidth: '200px',
                truncate: true, priority: 3, minBreakpoint: 'sm'
            },
            'part_name': {
                width: '180px', minWidth: '150px', maxWidth: '250px',
                truncate: true, priority: 3, minBreakpoint: 'sm'
            },
            'part_number': {
                width: '150px', minWidth: '130px', maxWidth: '200px',
                truncate: true, priority: 4, minBreakpoint: 'sm'
            },
            'verified': {
                width: '100px', minWidth: '90px', maxWidth: '120px',
                truncate: false, priority: 4, minBreakpoint: 'sm'
            },

            // Low priority columns - hide on medium screens and below
            'manufacturer': {
                width: '150px', minWidth: '130px', maxWidth: '200px',
                truncate: true, priority: 5, minBreakpoint: 'md'
            },
            'category': {
                width: '150px', minWidth: '130px', maxWidth: '200px',
                truncate: true, priority: 5, minBreakpoint: 'md'
            },
            'display_type': {
                width: '130px', minWidth: '110px', maxWidth: '180px',
                truncate: true, priority: 6, minBreakpoint: 'lg'
            },
            'display_size': {
                width: '130px', minWidth: '110px', maxWidth: '180px',
                truncate: true, priority: 6, minBreakpoint: 'lg'
            },
            'location': {
                width: '130px', minWidth: '110px', maxWidth: '180px',
                truncate: true, priority: 7, minBreakpoint: 'lg'
            },
            'notes': {
                width: '200px', minWidth: '150px', maxWidth: '300px',
                truncate: true, priority: 8, minBreakpoint: 'xl'
            }
        };

        // Use the calculated width based on label length if it's larger than the default
        const defaultConfig = defaults[columnKey] || {
            width: '150px', minWidth: '130px', maxWidth: '200px',
            truncate: true, priority: 5, minBreakpoint: 'md'
        };

        // Ensure minWidth is at least as large as the label-based width
        if (parseInt(labelBasedWidth) > parseInt(defaultConfig.minWidth || '0')) {
            defaultConfig.minWidth = labelBasedWidth;
        }

        return defaultConfig;
    };

    // Get responsive CSS class for column based on priority and breakpoint
    const getResponsiveClass = (config: ColumnConfig): string => {
        const breakpoint = config.minBreakpoint || 'xs';
        const priority = config.priority || 5;

        // High priority columns (1-2) are always visible
        if (priority <= 2) {
            return '';
        }

        // Medium priority columns (3-4) hide on small screens
        if (priority <= 4) {
            switch (breakpoint) {
                case 'sm':
                    return 'hidden sm:table-cell';
                case 'md':
                    return 'hidden md:table-cell';
                case 'lg':
                    return 'hidden lg:table-cell';
                case 'xl':
                    return 'hidden xl:table-cell';
                default:
                    return 'hidden sm:table-cell';
            }
        }

        // Low priority columns (5+) hide on medium screens and below
        switch (breakpoint) {
            case 'sm':
                return 'hidden md:table-cell';
            case 'md':
                return 'hidden lg:table-cell';
            case 'lg':
                return 'hidden lg:table-cell';
            case 'xl':
                return 'hidden xl:table-cell';
            case 'xs':
            default:
                return 'hidden md:table-cell';
        }
    };

    // Get column CSS classes based on content type and responsive behavior
    const getColumnClasses = (columnKey: string, config: ColumnConfig, isHeader: boolean = false): string => {
        const baseClasses = isHeader
            ? 'text-left font-semibold text-blue-900 dark:text-blue-100'
            : 'text-sm text-gray-600 dark:text-gray-300';

        const responsiveClass = getResponsiveClass(config);

        return `${baseClasses} ${responsiveClass}`.trim();
    };

    // Get column value from model/part data
    const getColumnValue = (columnKey: string, config: ColumnConfig, model: MobileModel): React.ReactNode => {
        const source = config.source || '';
        const defaultConfig = getDefaultColumnConfig(columnKey, config);
        const shouldTruncate = config.truncate !== undefined ? config.truncate : defaultConfig.truncate;

        switch (source) {
            case 'model.brand.name':
                const brandName = model.brand.name;
                const displayBrandName = shouldTruncate ? truncateText(brandName, columnKey, 15) : brandName;
                return (
                    <div className="flex items-center gap-2 min-w-0">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded flex items-center justify-center flex-shrink-0">
                            <Smartphone className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <span
                            className="font-semibold text-gray-900 dark:text-gray-100 text-sm truncate"
                            title={brandName}
                        >
                            {displayBrandName}
                        </span>
                    </div>
                );

            case 'model.name':
                const modelName = model.name;
                const displayModelName = shouldTruncate ? truncateText(modelName, columnKey, 20) : modelName;
                return (
                    <div className="min-w-0">
                        <p
                            className="font-medium text-gray-800 dark:text-gray-200 text-sm truncate"
                            title={modelName}
                        >
                            {displayModelName}
                        </p>
                        {/* Show model number on mobile for model column */}
                        {model.model_number && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 font-mono sm:hidden truncate">
                                {shouldTruncate ? truncateText(model.model_number, columnKey, 15) : model.model_number}
                            </p>
                        )}
                    </div>
                );

            case 'model.model_number':
                const modelNumber = model.model_number || '-';
                const displayModelNumber = shouldTruncate ? truncateText(modelNumber, columnKey, 15) : modelNumber;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 font-mono block truncate"
                        title={modelNumber}
                    >
                        {displayModelNumber}
                    </span>
                );

            case 'part.name':
                const partName = part.name || '-';
                const displayPartName = shouldTruncate ? truncateText(partName, columnKey, 18) : partName;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={partName}
                    >
                        {displayPartName}
                    </span>
                );

            case 'part.part_number':
                const partNumber = part.part_number || '-';
                const displayPartNumber = shouldTruncate ? truncateText(partNumber, columnKey, 15) : partNumber;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 font-mono block truncate"
                        title={partNumber}
                    >
                        {displayPartNumber}
                    </span>
                );

            case 'part.manufacturer':
                const manufacturer = part.manufacturer || '-';
                const displayManufacturer = shouldTruncate ? truncateText(manufacturer, columnKey, 16) : manufacturer;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={manufacturer}
                    >
                        {displayManufacturer}
                    </span>
                );

            case 'part.description':
                const description = part.description || '-';
                const displayDescription = shouldTruncate ? truncateText(description, columnKey, 25) : description;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={description}
                    >
                        {displayDescription}
                    </span>
                );

            case 'part.category.name':
                const categoryName = part.category.name || '-';
                const displayCategoryName = shouldTruncate ? truncateText(categoryName, columnKey, 15) : categoryName;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={categoryName}
                    >
                        {displayCategoryName}
                    </span>
                );

            case 'model.pivot.compatibility_notes':
                const notes = model.pivot?.compatibility_notes || '-';
                const displayNotes = shouldTruncate ? truncateText(notes, columnKey, 25) : notes;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={notes}
                    >
                        {displayNotes}
                    </span>
                );

            case 'model.pivot.display_type':
                const displayType = model.pivot?.display_type || '-';
                const displayDisplayType = shouldTruncate ? truncateText(displayType, columnKey, 12) : displayType;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={displayType}
                    >
                        {displayDisplayType}
                    </span>
                );

            case 'model.pivot.display_size':
                const displaySize = model.pivot?.display_size || '-';
                const displayDisplaySize = shouldTruncate ? truncateText(displaySize, columnKey, 12) : displaySize;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={displaySize}
                    >
                        {displayDisplaySize}
                    </span>
                );

            case 'model.pivot.location':
                const location = model.pivot?.location || '-';
                const displayLocation = shouldTruncate ? truncateText(location, columnKey, 15) : location;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={location}
                    >
                        {displayLocation}
                    </span>
                );

            case 'model.pivot.is_compatible':
                // For compatibility table, all models shown are compatible by default
                // This column would show compatibility status if we had incompatible models
                return (
                    <Badge variant="default" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 text-xs">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Compatible
                    </Badge>
                );

            case 'model.pivot.is_verified':
                return model.pivot?.is_verified ? (
                    <Badge variant="default" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 text-xs">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Verified
                    </Badge>
                ) : (
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800 text-xs">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Unverified
                    </Badge>
                );

            case 'model.pivot.front_camera_type':
                const frontCameraType = model.pivot?.front_camera_type || '-';
                const displayFrontCameraType = shouldTruncate ? truncateText(frontCameraType, columnKey, 15) : frontCameraType;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={frontCameraType}
                    >
                        {displayFrontCameraType}
                    </span>
                );

            case 'model.pivot.camera_position':
                const cameraPosition = model.pivot?.camera_position || '-';
                const displayCameraPosition = shouldTruncate ? truncateText(cameraPosition, columnKey, 15) : cameraPosition;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={cameraPosition}
                    >
                        {displayCameraPosition}
                    </span>
                );

            case 'model.pivot.battery_mah':
                const batteryMah = model.pivot?.battery_mah || '-';
                const displayBatteryMah = shouldTruncate ? truncateText(batteryMah, columnKey, 12) : batteryMah;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={batteryMah}
                    >
                        {displayBatteryMah}
                    </span>
                );

            case 'model.pivot.pin_model':
                const pinModel = model.pivot?.pin_model || '-';
                const displayPinModel = shouldTruncate ? truncateText(pinModel, columnKey, 15) : pinModel;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={pinModel}
                    >
                        {displayPinModel}
                    </span>
                );

            case 'model.pivot.connector_types':
                const connectorTypes = model.pivot?.connector_types || '-';
                const displayConnectorTypes = shouldTruncate ? truncateText(connectorTypes, columnKey, 15) : connectorTypes;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={connectorTypes}
                    >
                        {displayConnectorTypes}
                    </span>
                );

            case 'model.pivot.types':
                const types = model.pivot?.types || '-';
                const displayTypes = shouldTruncate ? truncateText(types, columnKey, 12) : types;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={types}
                    >
                        {displayTypes}
                    </span>
                );

            case 'model.pivot.additional_info':
                const additionalInfo = model.pivot?.additional_info || '-';
                const displayAdditionalInfo = shouldTruncate ? truncateText(additionalInfo, columnKey, 25) : additionalInfo;
                return (
                    <span
                        className="text-sm text-gray-600 dark:text-gray-300 block truncate"
                        title={additionalInfo}
                    >
                        {displayAdditionalInfo}
                    </span>
                );

            default:
                return '-';
        }
    };

    const visibleColumns = getVisibleColumns();

    if (visibleColumns.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No columns configured to display
            </div>
        );
    }

    if (!part.models || part.models.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No compatible models found for this part
            </div>
        );
    }

    return (
        <div className={`border border-gray-400 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-900 ${className}`}>
            <div className="overflow-x-auto">
                <table ref={tableRef} className="w-full border-collapse compatibility-table" style={{ tableLayout: 'fixed' }}>
                    <thead className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40">
                        <tr>
                            {visibleColumns.map(([columnKey, config], index) => {
                                const defaultConfig = getDefaultColumnConfig(columnKey, config);
                                const columnStyles = getColumnWidthStyles(columnKey, {
                                    ...defaultConfig,
                                    ...config
                                }, true); // Pass true for header

                                return (
                                    <th
                                        key={columnKey}
                                        data-column-key={columnKey}
                                        style={columnStyles}
                                        className={`p-3 align-top border-r border-gray-400 dark:border-gray-600 relative ${index === 0 ? 'first:rounded-tl-lg' : ''
                                            } ${index === visibleColumns.length - 1 ? 'last:rounded-tr-lg border-r-0' : ''
                                            } ${getColumnClasses(columnKey, { ...defaultConfig, ...config }, true)}`}
                                    >
                                        <div className="flex items-center gap-2 min-w-0">
                                            <span className="font-medium text-sm whitespace-nowrap overflow-hidden text-ellipsis" title={config.label}>
                                                {config.label}
                                            </span>
                                            {isAdminView && (
                                                <button
                                                    onClick={() => handleColumnToggle(columnKey)}
                                                    disabled={isUpdatingColumn === columnKey || (config.required && config.enabled)}
                                                    className={`flex items-center flex-shrink-0 p-1 rounded transition-colors ${config.required && config.enabled
                                                        ? 'cursor-not-allowed opacity-50'
                                                        : 'hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer'
                                                        } ${isUpdatingColumn === columnKey ? 'opacity-50' : ''}`}
                                                    title={
                                                        config.required && config.enabled
                                                            ? "Required column cannot be disabled"
                                                            : config.enabled
                                                                ? "Click to disable column"
                                                                : "Click to enable column"
                                                    }
                                                >
                                                    {isUpdatingColumn === columnKey ? (
                                                        <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                                                    ) : config.enabled ? (
                                                        <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                                                    ) : (
                                                        <X className="w-3 h-3 text-red-600 dark:text-red-400" />
                                                    )}
                                                </button>
                                            )}
                                        </div>

                                        {/* Column resize handle */}
                                        {enableColumnResize && index < visibleColumns.length - 1 && (
                                            <div
                                                className="absolute top-0 right-0 w-2 h-full cursor-col-resize hover:bg-blue-500/30 flex items-center justify-center group border-r border-transparent hover:border-blue-500/50 transition-all duration-200"
                                                onMouseDown={(e) => handleMouseDown(e, columnKey)}
                                                title="Drag to resize column"
                                            >
                                                <div className="w-0.5 h-4 bg-gray-300 group-hover:bg-blue-500 transition-colors duration-200"></div>
                                            </div>
                                        )}
                                    </th>
                                );
                            })}
                        </tr>
                    </thead>
                    <tbody>
                        {(part.models || []).map((model, modelIndex) => {
                            const isBlurred = model.is_blurred || false;
                            const blurClass = isBlurred ? 'blur-sm' : '';
                            const totalModels = part.models?.length || 0;

                            return (
                                <tr
                                    key={model.id}
                                    className={`border-b border-gray-400 dark:border-gray-600 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${modelIndex % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/50'
                                        } ${modelIndex === totalModels - 1 ? 'last:border-b-0' : ''} ${blurClass}`}
                                >
                                    {visibleColumns.map(([columnKey, config], colIndex) => {
                                        const defaultConfig = getDefaultColumnConfig(columnKey, config);
                                        const columnStyles = getColumnWidthStyles(columnKey, {
                                            ...defaultConfig,
                                            ...config
                                        });

                                        return (
                                            <td
                                                key={`${columnKey}-${forceUpdate}`}
                                                style={columnStyles}
                                                className={`p-3 align-top ${colIndex < visibleColumns.length - 1 ? 'border-r border-gray-400 dark:border-gray-600' : ''
                                                    } ${getColumnClasses(columnKey, { ...defaultConfig, ...config })}`}
                                            >
                                                <div className="min-w-0 max-w-full overflow-hidden">
                                                    {getColumnValue(columnKey, config, model)}
                                                </div>
                                            </td>
                                        );
                                    })}
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default DynamicCompatibilityTable;
