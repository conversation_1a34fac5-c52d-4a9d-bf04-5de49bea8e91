import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle, Check, X, Save, RotateCcw, GripVertical } from 'lucide-react';
import { toast } from 'sonner';
import type { CompatibilityColumns, ColumnConfig } from '@/types';

interface CompatibilityColumnsManagerProps {
    initialColumns: CompatibilityColumns;
    onUpdate: (columns: CompatibilityColumns) => void;
    isUpdating?: boolean;
}

const CompatibilityColumnsManager: React.FC<CompatibilityColumnsManagerProps> = ({
    initialColumns,
    onUpdate,
    isUpdating = false
}) => {
    const [columns, setColumns] = useState<CompatibilityColumns>(initialColumns);
    const [hasChanges, setHasChanges] = useState(false);

    // Handle column toggle
    const handleColumnToggle = useCallback((columnKey: string) => {
        const column = columns[columnKey];
        if (!column) return;

        // Don't allow disabling required columns
        if (column.required && column.enabled) {
            toast.error('Required columns cannot be disabled');
            return;
        }

        const updatedColumns = {
            ...columns,
            [columnKey]: {
                ...column,
                enabled: !column.enabled
            }
        };

        setColumns(updatedColumns);
        setHasChanges(true);
    }, [columns]);

    // Handle label change
    const handleLabelChange = useCallback((columnKey: string, newLabel: string) => {
        const updatedColumns = {
            ...columns,
            [columnKey]: {
                ...columns[columnKey],
                label: newLabel
            }
        };

        setColumns(updatedColumns);
        setHasChanges(true);
    }, [columns]);

    // Handle order change
    const handleOrderChange = useCallback((columnKey: string, newOrder: number) => {
        const updatedColumns = {
            ...columns,
            [columnKey]: {
                ...columns[columnKey],
                order: newOrder
            }
        };

        setColumns(updatedColumns);
        setHasChanges(true);
    }, [columns]);

    // Save changes
    const handleSave = useCallback(async () => {
        try {
            await onUpdate(columns);
            setHasChanges(false);
            toast.success('Column configuration updated successfully');
        } catch (error) {
            toast.error('Failed to update column configuration');
        }
    }, [columns, onUpdate]);

    // Reset changes
    const handleReset = useCallback(() => {
        setColumns(initialColumns);
        setHasChanges(false);
        toast.info('Changes reset');
    }, [initialColumns]);

    // Get sorted columns for display
    const sortedColumns = Object.entries(columns).sort(([, a], [, b]) => 
        (a.order || 999) - (b.order || 999)
    );

    // Count enabled columns
    const enabledCount = Object.values(columns).filter(col => col.enabled).length;
    const totalCount = Object.keys(columns).length;

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="flex items-center gap-2">
                            Compatibility Table Columns
                            <Badge variant="secondary">
                                {enabledCount}/{totalCount} enabled
                            </Badge>
                        </CardTitle>
                        <CardDescription>
                            Configure which columns are visible in the compatibility tables on both admin and public pages.
                            Required columns cannot be disabled.
                        </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                        {hasChanges && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleReset}
                                disabled={isUpdating}
                            >
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Reset
                            </Button>
                        )}
                        <Button
                            onClick={handleSave}
                            disabled={!hasChanges || isUpdating}
                            size="sm"
                        >
                            <Save className="h-4 w-4 mr-2" />
                            {isUpdating ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {hasChanges && (
                    <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                        <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm font-medium">You have unsaved changes</span>
                        </div>
                    </div>
                )}

                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-12">Order</TableHead>
                            <TableHead>Column</TableHead>
                            <TableHead>Label</TableHead>
                            <TableHead className="w-20">Status</TableHead>
                            <TableHead className="w-20">Required</TableHead>
                            <TableHead className="w-24">Enabled</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedColumns.map(([columnKey, config]) => (
                            <TableRow key={columnKey}>
                                <TableCell>
                                    <Input
                                        type="number"
                                        value={config.order || 1}
                                        onChange={(e) => handleOrderChange(columnKey, parseInt(e.target.value) || 1)}
                                        className="w-16 text-center"
                                        min="1"
                                        max="20"
                                    />
                                </TableCell>
                                <TableCell>
                                    <div className="flex items-center gap-2">
                                        <GripVertical className="h-4 w-4 text-gray-400" />
                                        <code className="text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                            {columnKey}
                                        </code>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <Input
                                        value={config.label}
                                        onChange={(e) => handleLabelChange(columnKey, e.target.value)}
                                        className="max-w-xs"
                                        placeholder="Column label"
                                    />
                                </TableCell>
                                <TableCell>
                                    {config.enabled ? (
                                        <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            <Check className="h-3 w-3 mr-1" />
                                            Enabled
                                        </Badge>
                                    ) : (
                                        <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                            <X className="h-3 w-3 mr-1" />
                                            Disabled
                                        </Badge>
                                    )}
                                </TableCell>
                                <TableCell>
                                    {config.required ? (
                                        <Badge variant="destructive" className="text-xs">
                                            Required
                                        </Badge>
                                    ) : (
                                        <Badge variant="outline" className="text-xs">
                                            Optional
                                        </Badge>
                                    )}
                                </TableCell>
                                <TableCell>
                                    <Switch
                                        checked={config.enabled}
                                        onCheckedChange={() => handleColumnToggle(columnKey)}
                                        disabled={config.required && config.enabled}
                                    />
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>

                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                        <strong>Note:</strong> Changes to column configuration will affect both admin and public compatibility tables.
                        Admin users will always see all columns (enabled and disabled), while public users will only see enabled columns.
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default CompatibilityColumnsManager;
