
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckIcon, XIcon } from 'lucide-react';

export interface ColumnInfo {
    name: string;
    sampleData: string[];
    isRequired: boolean;
    mappedTo: string;
    purpose: string;
    isSelected: boolean;
}

interface ColumnSelectionTableProps {
    columns: ColumnInfo[];
    onColumnToggle: (columnName: string, isSelected: boolean) => void;
    onSelectAll: () => void;
    onDeselectAll: () => void;
}

export default function ColumnSelectionTable({
    columns,
    onColumnToggle,
    onSelectAll,
    onDeselectAll
}: ColumnSelectionTableProps) {
    const selectedCount = columns.filter(col => col.isSelected).length;
    const totalCount = columns.length;
    const requiredSelectedCount = columns.filter(col => col.isRequired && col.isSelected).length;
    const totalRequiredCount = columns.filter(col => col.isRequired).length;

    return (
        <div className="space-y-4">
            {/* Header with selection controls */}
            <div className="flex items-center justify-start">
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onSelectAll}
                        disabled={selectedCount === totalCount}
                    >
                        <CheckIcon className="h-4 w-4 mr-1" />
                        Select All
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onDeselectAll}
                        disabled={selectedCount === 0}
                    >
                        <XIcon className="h-4 w-4 mr-1" />
                        Deselect All
                    </Button>
                </div>
            </div>

            {/* Quick Column Selection Row */}
            <div className="space-y-2">
                <div className="flex flex-wrap gap-3 p-3 bg-muted/30 rounded-lg border">
                    {columns.map((column) => (
                        <label
                            key={column.name}
                            className="flex items-center gap-2 cursor-pointer hover:bg-background/50 px-2 py-1 rounded transition-colors"
                        >
                            <input
                                type="checkbox"
                                checked={column.isSelected}
                                onChange={(e) => onColumnToggle(column.name, e.target.checked)}
                                disabled={column.isRequired && column.isSelected}
                                className="rounded border-gray-300 text-primary focus:ring-primary focus:ring-offset-0"
                            />
                            <span className={`text-sm ${column.isRequired ? 'font-medium' : ''}`}>
                                {column.name}
                                {column.isRequired && <span className="text-red-500 ml-1">*</span>}
                            </span>
                        </label>
                    ))}
                </div>
            </div>

            {/* Selection summary */}
            <div className="flex items-center gap-4 text-sm">
                <Badge variant="secondary">
                    {selectedCount} of {totalCount} columns selected
                </Badge>
                <Badge variant={requiredSelectedCount === totalRequiredCount ? "default" : "destructive"}>
                    {requiredSelectedCount} of {totalRequiredCount} required columns selected
                </Badge>
            </div>

            {/* Warning for missing required columns */}
            {requiredSelectedCount < totalRequiredCount && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-700">
                        <strong>Warning:</strong> You must select all required columns (Brand, Model, Compatible) to proceed with the import.
                    </p>
                </div>
            )}
        </div>
    );
}
